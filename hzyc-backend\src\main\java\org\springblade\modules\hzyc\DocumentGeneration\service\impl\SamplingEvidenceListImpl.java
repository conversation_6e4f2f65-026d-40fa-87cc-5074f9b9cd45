package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽样取证物品清单文档生成实现类
 *
 * <AUTHOR>
 */
@Service("samplingListDocument")
public class SamplingEvidenceListImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "13抽样取证物品清单.docx";
    }

    @Override
    public String getDocumentType() {
        return "SAMPLING-EVIDENCE-LIST";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
		query.put("AJBS", caseId);
		JSONArray array = icaseInfoService.getCaseSamplingItemList(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMap = new HashMap<>();

		fieldMap.put("CYQDMXBS", "handle_dtl_uuid");
		fieldMap.put("AJMC", "case_name");
		fieldMap.put("DSR", "party");
		fieldMap.put("AY", "cause_of_action");
		fieldMap.put("KZZD1", "ext1");
		fieldMap.put("ND", "doc_year");
		fieldMap.put("CYQZWPQDBS", "handle_uuid");
		fieldMap.put("TZZD", "ext_json");
		fieldMap.put("AJBS", "case_uuid");
		fieldMap.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMap.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMap.put("BZXS", "pack_style");
		fieldMap.put("SJBM", "city_org_code");
		fieldMap.put("WSRQ", "doc_date");
		fieldMap.put("XGR", "modifier");
		fieldMap.put("XGSJ", "modify_time");
		fieldMap.put("CYNR", "sample_content");
		fieldMap.put("KZZD3", "ext3");
		fieldMap.put("WPUUID", "goods_uuid");
		fieldMap.put("SXXZFLDL", "prop_type");
		fieldMap.put("WPFL", "goods_type");
		fieldMap.put("CYRQ", "sample_date");
		fieldMap.put("WSHQ", "full_doc_no");
		fieldMap.put("MCRKSJ", "mc_tec_ctime");
		fieldMap.put("KZZD2", "ext2");
		fieldMap.put("XYWYBS", "tid");
		fieldMap.put("SXXZFLXL", "prop_subtype");
		fieldMap.put("CLJG", "sample_result");
		fieldMap.put("WPMXBS", "goods_dtl_uuid");
		fieldMap.put("WSH", "doc_no");
		fieldMap.put("SJMC", "city_org_name");
		fieldMap.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMap.put("XH", "order_index");
		fieldMap.put("GG", "spec");
		fieldMap.put("BZ", "remark");
		fieldMap.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMap.put("CBRUUID", "undertaker_uuids");
		fieldMap.put("DW", "unit");
		fieldMap.put("LABH", "case_code");
		fieldMap.put("SFYX", "is_active");
		fieldMap.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMap.put("YPJS", "qty");
		fieldMap.put("CBR", "undertaker");
		fieldMap.put("TAR", "same_party");
		fieldMap.put("CYSL", "handle_qty");
		fieldMap.put("DWJC", "org_short_name");
		fieldMap.put("CYDD", "address");
		fieldMap.put("CJR", "creator");
		fieldMap.put("DWSXZ", "org_abbr");
		fieldMap.put("WPMC", "goods_name");
		fieldMap.put("CBRZFZH", "undertaker_insp_no");
		fieldMap.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMap.put("WSZT", "finsh_status");
		fieldMap.put("CYMD", "sample_dest");
		fieldMap.put("WFGD", "legal_argument");
		fieldMap.put("WSCJSJ", "create_time");

		return fieldMap;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("sampling_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟抽﹝2025﹞第48号");
        mockData.put("doc_date", "2025年6月10日");

        // 案件信息
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：************，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 抽样信息
        mockData.put("sampling_time", "2025年03月18日");
        mockData.put("sampling_location", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("sampling_reason", "涉嫌违法经营卷烟，需要抽样取证进行检验鉴定");
        mockData.put("undertaker", "叶辉明,朱兆强");
        mockData.put("undertaker_insp_no", "19090352015,19090352023");

        // 抽样物品清单
        List<Map<String, Object>> samplingItems = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("order_index", 1);
        item1.put("goods_name", "黄果树(长征)");
        item1.put("spec", "20支/包，10包/条");
        item1.put("unit", "条");
        item1.put("sampling_qty", 5.0);
        item1.put("total_qty", 200.0);
        item1.put("price", 100.0);
        item1.put("sampling_amt", 500.0);
        item1.put("batch_no", "20250315001");
        item1.put("production_date", "2025-03-15");
        item1.put("sampling_purpose", "检验真伪");
        item1.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("order_index", 2);
        item2.put("goods_name", "白沙(硬精品三代)");
        item2.put("spec", "20支/包，10包/条");
        item2.put("unit", "条");
        item2.put("sampling_qty", 3.0);
        item2.put("total_qty", 150.0);
        item2.put("price", 120.0);
        item2.put("sampling_amt", 360.0);
        item2.put("batch_no", "20250316002");
        item2.put("production_date", "2025-03-16");
        item2.put("sampling_purpose", "检验真伪");
        item2.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("order_index", 3);
        item3.put("goods_name", "红塔山(硬经典)");
        item3.put("spec", "20支/包，10包/条");
        item3.put("unit", "条");
        item3.put("sampling_qty", 3.0);
        item3.put("total_qty", 150.0);
        item3.put("price", 80.0);
        item3.put("sampling_amt", 240.0);
        item3.put("batch_no", "20250317003");
        item3.put("production_date", "2025-03-17");
        item3.put("sampling_purpose", "检验真伪");
        item3.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item3);

        mockData.put("sampling_items", samplingItems);

        // 统计信息
        mockData.put("total_sampling_qty", 11.0);
        mockData.put("total_sampling_amt", 1100.0);
        mockData.put("total_goods_qty", 500.0);
        mockData.put("sampling_ratio", "2.2%");

        // 抽样方法
        mockData.put("sampling_method", "随机抽样");
        mockData.put("sampling_standard", "按照《烟草专卖品抽样检验管理办法》执行");

        // 保存方式
        mockData.put("storage_method", "密封保存");
        mockData.put("storage_location", "广东省博罗县烟草专卖局证据保管室");
        mockData.put("storage_condition", "常温、干燥、避光保存");

        // 检验机构
        mockData.put("inspection_org", "广东省烟草质量监督检测站");
        mockData.put("inspection_contact", "020-12345678");

        // 法律依据
        mockData.put("legal_basis", "《中华人民共和国行政处罚法》第三十七条、《烟草专卖行政处罚程序规定》第十九条、《烟草专卖品抽样检验管理办法》第八条");

        // 备注说明
        mockData.put("notice_content", "1. 抽样过程全程录像；2. 样品密封后由当事人签字确认；3. 检验结果将作为案件处理依据；4. 当事人对抽样过程无异议。");

        // 当事人确认
        mockData.put("party_signature", "梁俊强");
        mockData.put("party_signature_time", "2025年03月18日");
        mockData.put("party_opinion", "对抽样过程无异议");

        // 执法人员
        mockData.put("executor1_name", "叶辉明");
        mockData.put("executor1_insp_no", "19090352015");
        mockData.put("executor2_name", "朱兆强");
        mockData.put("executor2_insp_no", "19090352023");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sysisdelete", "");
        mockData.put("sysupdatetime", "");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}
