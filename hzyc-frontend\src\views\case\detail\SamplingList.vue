<template>
  <div class="penalty-decision-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>抽样取证物品清单</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_shortname"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>抽样取证物品清单</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.full_doc_no"
                placeholder="文号前缀"
                style="width: 200px;"
              />
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.legal_basis"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="根据《中华人民共和国行政处罚法》第五十六条的规定，现对下列物品予以抽样取证："
                class="legal-basis auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 当事人信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.party_info"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="当事人信息"
                class="party-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 案由 -->
          <div class="content-section case-section">
            <span class="case-label">立案编号：</span>
            <el-input
              v-model="formData.case_number"
              placeholder="立案编号"
              class="case-input"
            />
          </div>

          <!-- 抽样方法和目的 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.sampling_method_purpose"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="抽样方法和抽样目的"
                class="sampling-method auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 抽样时间 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.sampling_time"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="抽样时间详情"
                class="sampling-time auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 抽样地点 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.sampling_location"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="抽样地点"
                class="sampling-location auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 样品清单 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.sample_list"
                type="textarea"
                :autosize="{ minRows: 6 }"
                placeholder="样品清单详情"
                class="sample-list auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="备注信息"
                class="remark auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 执法人员信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.officer_info"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="执法人员信息"
                class="officer-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 送达信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.delivery_info"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="送达信息"
                class="delivery-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_modify_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>



<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  full_doc_no:'',
  party_info: '',
  case_number: '',
  legal_basis: '',
  sampling_method_purpose: '',
  sampling_time: '',
  sampling_location: '',
  sample_list: '',
  remark: '',
  officer_info: '',
  delivery_info: '',
  org_shortname: '',
  sys_modify_time: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      party_info: docContent.party_info || docContent.partyInfo || newVal.party_info || '',
      case_number: docContent.case_number || docContent.caseNumber || newVal.case_number || '',
      legal_basis: docContent.legal_basis || docContent.legalBasis || newVal.legal_basis || '',
      sampling_method_purpose: docContent.sampling_method_purpose || docContent.samplingMethodPurpose || newVal.sampling_method_purpose || '',
      sampling_time: docContent.sampling_time || docContent.samplingTime || newVal.sampling_time || '',
      sampling_location: docContent.sampling_location || docContent.samplingLocation || newVal.sampling_location || '',
      sample_list: docContent.sample_list || docContent.sampleList || newVal.sample_list || '',
      remark: docContent.remark || newVal.remark || '',
      officer_info: docContent.officer_info || docContent.officerInfo || newVal.officer_info || '',
      delivery_info: docContent.delivery_info || docContent.deliveryInfo || newVal.delivery_info || '',
      org_shortname: docContent.org_shortname || docContent.orgShortname || newVal.org_shortname || '',
      sys_modify_time: docContent.sys_modify_time || docContent.sysModifyTime || newVal.sys_modify_time || '',
      full_doc_no: docContent.full_doc_no || docContent.fullDocNo || newVal.full_doc_no || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    party_info: formData.value.party_info,
    case_number: formData.value.case_number,
    legal_basis: formData.value.legal_basis,
    full_doc_no: formData.value.full_doc_no,
    sampling_method_purpose: formData.value.sampling_method_purpose,
    sampling_time: formData.value.sampling_time,
    sampling_location: formData.value.sampling_location,
    sample_list: formData.value.sample_list,
    remark: formData.value.remark,
    officer_info: formData.value.officer_info,
    delivery_info: formData.value.delivery_info,
    org_shortname: formData.value.org_shortname,
    sys_modify_time: formData.value.sys_modify_time
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
</style>
